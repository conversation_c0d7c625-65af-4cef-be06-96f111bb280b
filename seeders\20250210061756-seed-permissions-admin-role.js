"use strict";

const permissions = require("../config/permissions");
const { v4: uuidv4 } = require("uuid");
const bcrypt = require("bcrypt");
const config = require("../config/config");
const tykService = require("../services/tyk.service");
const logger = require("../config/logger");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Create Admin Role (or get existing one)
      const [adminRole] = await queryInterface.sequelize.query(
        `SELECT * FROM role WHERE name = 'admin' LIMIT 1`,
        { transaction }
      );

      let adminRoleId;
      if (adminRole.length > 0) {
        adminRoleId = adminRole[0].role_id;
      } else {
        adminRoleId = uuidv4();
        await queryInterface.bulkInsert(
          "role",
          [
            {
              role_id: adminRoleId,
              name: "admin",
              description: "Administrator role with all permissions",
              is_active: true,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );
      }

      // Create Kiosk Role (or get existing one)
      const [kioskRole] = await queryInterface.sequelize.query(
        `SELECT * FROM role WHERE name = 'kiosk' LIMIT 1`,
        { transaction }
      );
      let kioskRoleId;
      if (kioskRole.length > 0) {
        kioskRoleId = kioskRole[0].role_id;
      } else {
        kioskRoleId = uuidv4();
        await queryInterface.bulkInsert(
          "role",
          [
            {
              role_id: kioskRoleId,
              name: "kiosk",
              description: "Kiosk role with access to /kiosk endpoint",
              is_active: true,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );
      }

      // Get existing permissions
      const [existingPerms] = await queryInterface.sequelize.query(
        `SELECT name, permission_id FROM permission`,
        { transaction }
      );

      const existingPermNames = existingPerms.map((p) => p.name);
      const existingPermMap = new Map(
        existingPerms.map((p) => [p.name, p.permission_id])
      );

      // Filter new permissions
      const newPermissions = permissions
        .filter((perm) => !existingPermNames.includes(perm))
        .map((perm) => ({
          permission_id: uuidv4(),
          name: perm,
          description: `Permission to ${perm.replace("_", " ")}`,
          created_at: new Date(),
          updated_at: new Date(),
        }));

      // Insert new permissions
      if (newPermissions.length > 0) {
        await queryInterface.bulkInsert("permission", newPermissions, {
          transaction,
        });
      }

      // Combine all permissions (new + existing) for role mapping
      const allPermissionRecords = [...existingPerms, ...newPermissions];

      // Get existing role-permission mappings to avoid duplicates
      const [existingRolePerms] = await queryInterface.sequelize.query(
        `SELECT permission_id FROM role_permission WHERE role_id = :role_id`,
        {
          replacements: { role_id: adminRoleId },
          transaction,
        }
      );
      const existingRolePermIds = new Set(
        existingRolePerms.map((rp) => rp.permission_id)
      );

      const rolePermissionsToInsert = allPermissionRecords
        .filter((perm) => !existingRolePermIds.has(perm.permission_id))
        .map((perm) => ({
          role_permission_id: uuidv4(),
          role_id: adminRoleId,
          permission_id: perm.permission_id,
          created_at: new Date(),
          updated_at: new Date(),
        }));

      if (rolePermissionsToInsert.length > 0) {
        await queryInterface.bulkInsert(
          "role_permission",
          rolePermissionsToInsert,
          { transaction }
        );
      }

      // Assign kiosk permissions (only /kiosk endpoint)
      // You may want to create a permission like 'access_kiosk' if not present
      let kioskPermissionId;
      const kioskPermName = "access_kiosk";
      let kioskPerm = allPermissionRecords.find((p) => p.name === kioskPermName);
      if (!kioskPerm) {
        kioskPermissionId = uuidv4();
        await queryInterface.bulkInsert(
          "permission",
          [
            {
              permission_id: kioskPermissionId,
              name: kioskPermName,
              description: "Permission to access /kiosk endpoint",
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );
      } else {
        kioskPermissionId = kioskPerm.permission_id;
      }

      // Assign kiosk permission to kiosk role if not already assigned
      const [existingKioskRolePerm] = await queryInterface.sequelize.query(
        `SELECT * FROM role_permission WHERE role_id = :role_id AND permission_id = :permission_id`,
        {
          replacements: { role_id: kioskRoleId, permission_id: kioskPermissionId },
          transaction,
        }
      );
      if (!existingKioskRolePerm.length) {
        await queryInterface.bulkInsert(
          "role_permission",
          [
            {
              role_permission_id: uuidv4(),
              role_id: kioskRoleId,
              permission_id: kioskPermissionId,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );
      }

      // Create Admin Identity (if not exists)
      const [existingAdmin] = await queryInterface.sequelize.query(
        `SELECT identity_id FROM identity WHERE first_name = 'admin'`,
        { transaction }
      );

      let adminIdentityId;

      if (existingAdmin.length === 0) {
        adminIdentityId = uuidv4();
        const generateEid = () => {
          const characters =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
          let eid = "";
          for (let i = 0; i < 8; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            eid += characters[randomIndex];
          }
          return eid;
        };
        const eid = generateEid();
        await queryInterface.bulkInsert(
          "identity",
          [
            {
              identity_id: adminIdentityId,
              first_name: "admin",
              email: "<EMAIL>",
              first_name: "Admin",
              last_name: "User",
              identity_type: 1,
              eid: eid,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        await queryInterface.bulkInsert(
          "identity_verification",
          [
            {
              identity_verification_id: uuidv4(),
              identity_id: adminIdentityId,
              password_hash: bcrypt.hashSync("Pa$$w0rd!", 8),
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        await queryInterface.bulkInsert(
          "identity_role",
          [
            {
              identity_role_id: uuidv4(),
              identity_id: adminIdentityId,
              role_id: adminRoleId,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        // Create Tyk policy for admin user if Tyk is enabled
        if (config.auth.mode === 'tyk') {
          try {
            const adminPermissions = permissions; // Admin gets all permissions
            const policyName = `policy_${adminIdentityId}`;
            await tykService.createPolicy(policyName, adminPermissions);
            logger.info(`Tyk policy created for admin identity: ${adminIdentityId}`);
          } catch (error) {
            logger.warn(`Failed to create Tyk policy for admin: ${error.message}`);
          }
        }
      } else {
        adminIdentityId = existingAdmin[0].identity_id;

        // Create Tyk policy for existing admin user if Tyk is enabled
        if (config.auth.mode === 'tyk') {
          try {
            const adminPermissions = permissions; // Admin gets all permissions
            const policyName = `policy_${adminIdentityId}`;
            await tykService.createPolicy(policyName, adminPermissions);
            logger.info(`Tyk policy created for existing admin identity: ${adminIdentityId}`);
          } catch (error) {
            logger.warn(`Failed to create Tyk policy for existing admin: ${error.message}`);
          }
        }
      }

      // Create Kiosk Identity (if not exists)
      const [existingKiosk] = await queryInterface.sequelize.query(
        `SELECT identity_id FROM identity WHERE email = '<EMAIL>'`,
        { transaction }
      );

      let kioskIdentityId;

      if (existingKiosk.length === 0) {
        kioskIdentityId = uuidv4();
        const generateEid = () => {
          const characters =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
          let eid = "";
          for (let i = 0; i < 8; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            eid += characters[randomIndex];
          }
          return eid;
        };
        const eid = generateEid();
        await queryInterface.bulkInsert(
          "identity",
          [
            {
              identity_id: kioskIdentityId,
              email: "<EMAIL>",
              first_name: "Kiosk",
              last_name: "User",
              identity_type: 1,
              eid: eid,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        await queryInterface.bulkInsert(
          "identity_verification",
          [
            {
              identity_verification_id: uuidv4(),
              identity_id: kioskIdentityId,
              password_hash: bcrypt.hashSync("Pa$$w0rd!", 8),
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        await queryInterface.bulkInsert(
          "identity_role",
          [
            {
              identity_role_id: uuidv4(),
              identity_id: kioskIdentityId,
              role_id: kioskRoleId,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );

        // Create Tyk policy for kiosk user if Tyk is enabled
        if (config.auth.mode === 'tyk') {
          try {
            const kioskPermissions = ['access_kiosk']; // Kiosk gets only kiosk permissions
            const policyName = `policy_${kioskIdentityId}`;
            await tykService.createPolicy(policyName, kioskPermissions);
            logger.info(`Tyk policy created for kiosk identity: ${kioskIdentityId}`);
          } catch (error) {
            logger.warn(`Failed to create Tyk policy for kiosk: ${error.message}`);
          }
        }
      } else {
        kioskIdentityId = existingKiosk[0].identity_id;

        // Create Tyk policy for existing kiosk user if Tyk is enabled
        if (config.auth.mode === 'tyk') {
          try {
            const kioskPermissions = ['access_kiosk']; // Kiosk gets only kiosk permissions
            const policyName = `policy_${kioskIdentityId}`;
            await tykService.createPolicy(policyName, kioskPermissions);
            logger.info(`Tyk policy created for existing kiosk identity: ${kioskIdentityId}`);
          } catch (error) {
            logger.warn(`Failed to create Tyk policy for existing kiosk: ${error.message}`);
          }
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("identity_role", null, { transaction });
      await queryInterface.bulkDelete("identity_verification", null, {
        transaction,
      });
      await queryInterface.bulkDelete("identity", null, { transaction });
      await queryInterface.bulkDelete("role_permission", null, {
        transaction,
      });
      await queryInterface.bulkDelete("permission", null, { transaction });
      await queryInterface.bulkDelete(
        "role",
        { name: { [Sequelize.Op.in]: ["admin", "kiosk"] } },
        { transaction }
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
